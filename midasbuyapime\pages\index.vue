<template>
  <div class="background-container">
    <!-- Header -->
    <div class="header-wrap fixed top-0 left-0 w-full h-[45px] bg-black/5 bg-[url('https://cdn.midasbuy.com/events/img/header-bg.png')] z-50">
      <div class="header h-full max-w-7xl mx-auto px-4">
        <div class="flex items-center justify-between h-full">
          <!-- Left side with logos -->
          <div class="flex items-center space-x-4">
            <a href="https://www.pubgmobile.com" class="flex-shrink-0">
              <img src="https://cdn.midasbuy.com/images/apps/pubgm/PUBGM_LOGO.png" alt="PUBG Mobile" class="h-[24px] w-auto">
            </a>
            <a href="https://www.midasbuy.com/midasbuy/buy/pubgm?adtag=event.point" class="flex-shrink-0">
              <img src="https://cdn.midasbuy.com/images/Midasbuy_basic_logo_color_light.4f5ede11.png" alt="MidasBuy" class="h-[24px] w-auto">
            </a>
          </div>
          
          <!-- Right side with social and language -->
          <div class="flex items-center space-x-4">
            <a href="https://www.facebook.com/Midasbuy" class="flex-shrink-0">
              <img src="https://cdn.midasbuy.com/events/img/fb-icon.png" alt="Facebook" class="h-[24px] w-auto">
            </a>
            <div class="flex items-center space-x-2">
              <img src="//cdn.midasbuy.com/oversea_web/static/images/country/en.14578e15c052ee949fc8858e2442de15.png" alt="English" class="h-[24px] w-auto">
              <span class="text-white text-sm">EN</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-center items-center min-h-screen px-4">
      <div class="lottery-container mt-[180px] md:mt-[250px] relative w-full max-w-[500px] mx-auto">
        <!-- Top row reward boxes -->
        <div class="flex justify-center gap-6 md:gap-12 mb-6 md:mb-8">
          <div v-for="index in 3" :key="`top-${index}`" 
               class="w-[80px] h-[80px] md:w-[100px] md:h-[100px] reward-box"
               :class="{ active: currentBox === index - 1 }">
            <img src="~/public/images/rewardboxess.png" alt="Reward Box" class="w-full h-full object-contain">
          </div>
        </div>

        <!-- Middle section with side reward boxes and main items -->
        <div class="flex justify-between items-center mb-6 md:mb-8">
          <!-- Left side reward box -->
          <div class="flex">
            <div class="w-[80px] h-[80px] md:w-[100px] md:h-[100px] reward-box"
                 :class="{ active: currentBox === 3 }">
              <img src="~/public/images/rewardboxess.png" alt="Reward Box" class="w-full h-full object-contain">
            </div>
          </div>

          <!-- Main items -->
          <div class="flex justify-center items-center mx-auto relative">
            <!-- Main Items Row -->
            <div class="flex justify-center items-center gap-6 md:gap-8">
              <div class="w-[90px] h-[150px] md:w-[120px] md:h-[200px] main-item"
                   :class="{ active: currentBox === 6 }">
                <img src="~/public/images/bape.jpg" alt="BAPE Camo Set" class="w-full h-full object-contain">
                <div class="item-name text-sm md:text-base">BAPE Camo Set</div>
              </div>
              <div class="w-[90px] h-[150px] md:w-[120px] md:h-[200px] main-item"
                   :class="{ active: currentBox === 7 }">
                <img src="~/public/images/m416.jpg" alt="M416 Upgrade" class="w-full h-full object-contain">
                <div class="item-name text-sm md:text-base">M416 Upgrade</div>
              </div>
            </div>
          </div>

          <!-- Right side reward box -->
          <div class="flex">
            <div class="w-[80px] h-[80px] md:w-[100px] md:h-[100px] reward-box"
                 :class="{ active: currentBox === 4 }">
              <img src="~/public/images/rewardboxess.png" alt="Reward Box" class="w-full h-full object-contain">
            </div>
          </div>
        </div>

        <!-- Bottom row reward box -->
        <div class="flex justify-center">
          <div class="w-[80px] h-[80px] md:w-[100px] md:h-[100px] reward-box"
               :class="{ active: currentBox === 5 }">
            <img src="~/public/images/rewardboxess.png" alt="Reward Box" class="w-full h-full object-contain">
          </div>
        </div>

        <!-- Bottom image -->
        <div class="flex justify-center mt-4">
          <button 
            @click="startDraw" 
            :disabled="isDrawing"
            class="w-[150px] h-[45px] md:w-[200px] md:h-[60px] cursor-pointer transform hover:scale-105 transition-transform duration-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <img 
              src="https://pagedoo.midasbuy.com/material/1450015065/a657d4976cb64c5e6ba816d3733e936d.png" 
              alt="Draw Reward" 
              class="w-full h-full object-contain"
            >
          </button>
        </div>
      </div>
    </div>

    <!-- Player ID Verification Popup -->
    <div v-if="showPlayerIdPopup" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4">
      <div class="relative bg-gray-800 rounded-lg w-full max-w-md mx-auto shadow-2xl overflow-hidden">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 pb-4">
          <h1 class="text-xl font-medium text-white">Enter Your Player ID Now</h1>
          <button @click="closePopup" class="text-gray-400 hover:text-white transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="px-6 pb-6">
          <!-- Player ID Label and Sign in link -->
          <div class="flex items-center justify-between mb-4">
            <span class="text-white text-base">Player ID</span>
            <button class="text-cyan-400 text-sm hover:text-cyan-300 transition-colors flex items-center">
              Sign in to access saved Player ID 
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            </button>
          </div>

          <!-- Info Box -->
          <div class="bg-gradient-to-r from-cyan-400 to-blue-500 rounded-t-lg p-0 mb-0">
            <p class="text-white text-sm font-medium">Please select or fill in your Player ID you want to recharge.</p>
          </div>

          <!-- Input Field -->
          <div class="mb-6">
            <div class="relative">
              <input 
                v-model="playerId"
                type="text"
                placeholder="Enter Player ID"
                class="w-full px-4 py-4 bg-gray-900 border-2 border-cyan-400 rounded-b-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 text-base font-medium"
                maxlength="10"
                @keyup.enter="verifyPlayerId"
              >
            </div>
          </div>

          <!-- OK Button -->
          <button 
            @click="verifyPlayerId"
            :disabled="!playerId.trim() || isVerifying"
            class="w-full bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 disabled:from-gray-600 disabled:to-gray-700 text-white py-3 px-6 rounded-lg font-medium transition-all duration-200 disabled:cursor-not-allowed text-base"
          >
            <span v-if="isVerifying" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Verifying...
            </span>
            <span v-else>OK</span>
          </button>

          <!-- Help Section -->
          <div class="mt-6">
            <div class="flex items-center mb-3">
              <div class="w-5 h-5 rounded-full border-2 border-gray-400 flex items-center justify-center mr-2">
                <span class="text-gray-400 text-xs">?</span>
              </div>
              <span class="text-white text-base">Couldn't find your Player ID?</span>
            </div>
            
            <p class="text-gray-300 text-sm mb-3">1.1. Enter the game</p>
            
            <!-- Game Screenshot -->
            <div class="rounded-lg overflow-hidden">
              <img 
                src="https://cdn.midasbuy.com/images/2.4d71ee03.jpg" 
                alt="PUBG Mobile Game Screenshot" 
                class="w-full h-32 object-cover"
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Snow background image -->
    <img 
      src="https://pagedoo.midasbuy.com/material/1450015065/ddb43342fb339a418f0adf51eabb1adb.jpg" 
      alt="Snow Background"
      class="snow-background"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const isDrawing = ref(false)
const currentBox = ref(-1)
const totalBoxes = 8 // Reduced number with larger boxes and main items
const showPlayerIdPopup = ref(true)
const playerId = ref('')
const isVerified = ref(false)
const isVerifying = ref(false)

// Always show popup on page load
onMounted(() => {
  showPlayerIdPopup.value = true
  
  // Pre-fill with saved Player ID if available
  const savedPlayerId = localStorage.getItem('pubg_player_id')
  if (savedPlayerId) {
    playerId.value = savedPlayerId
  }
})

const verifyPlayerId = async () => {
  if (!playerId.value.trim()) return
  
  isVerifying.value = true
  
  try {
    const data = await $fetch('/api/verify-player', {
      method: 'GET',
      query: {
        playerId: playerId.value.trim()
      }
    })
    
    // Check if the API response indicates a valid player ID
    if (data && data.error === false && data.status === 200 && data.msg === "id_found") {
      // Check if player is banned
      if (data.data && data.data.is_ban === 1) {
        alert('This Player ID is banned and cannot participate in the lottery.')
        return
      }
      
      // Save player ID and username
      localStorage.setItem('pubg_player_id', playerId.value.trim())
      if (data.data && data.data.username) {
        localStorage.setItem('pubg_player_username', data.data.username)
      }
      
      isVerified.value = true
      showPlayerIdPopup.value = false
      
      // Show success message with actual username
      setTimeout(() => {
        const username = data.data && data.data.username ? data.data.username : playerId.value
        alert(`Welcome ${username}! You can now participate in the lottery.`)
      }, 500)
    } else {
      // Invalid player ID
      alert('Invalid Player ID. Please check your Player ID and try again.')
    }
  } catch (error) {
    console.error('Player ID verification failed:', error)
    alert('Failed to verify Player ID. Please check your internet connection and try again.')
  } finally {
    isVerifying.value = false
  }
}

const closePopup = () => {
  // Allow closing but warn user
  const confirmed = confirm('You need to enter your Player ID to participate in the lottery. Are you sure you want to close?')
  if (confirmed) {
    showPlayerIdPopup.value = false
  }
}

const rewards = [
  { 
    name: 'BAPE Camo Set', 
    probability: 0.8,
    image: '/images/bape.jpg'
  },
  { 
    name: 'M416 Upgrade', 
    probability: 0.9,
    image: '/images/m416.jpg'
  },
  { 
    name: '50 UC', 
    probability: 0.1,
    image: '/images/uc50.png'
  },
  { 
    name: 'Silver Fragment', 
    probability: 0.05,
    image: '/images/paw.png'
  },
  { 
    name: 'BP Points', 
    probability: 0.05,
    image: '/images/bp.png'
  }
]

const startDraw = () => {
  if (!isVerified.value) {
    showPlayerIdPopup.value = true
    alert('Please verify your Player ID first!')
    return
  }
  
  if (isDrawing.value) return
  
  isDrawing.value = true
  currentBox.value = 0
  let cycles = 0
  const maxCycles = 2 // Number of full cycles before slowing down
  let speed = 100 // Initial speed in milliseconds
  
  const animate = () => {
    currentBox.value = (currentBox.value + 1) % totalBoxes
    
    // Count completed cycles
    if (currentBox.value === 0) {
      cycles++
    }
    
    // Gradually slow down after max cycles
    if (cycles >= maxCycles) {
      speed += 50
    }
    
    // Stop condition
    if (speed >= 500) {
      // Select final reward
      const random = Math.random()
      let probabilitySum = 0
      let selectedReward
      
      for (const reward of rewards) {
        probabilitySum += reward.probability
        if (random <= probabilitySum) {
          selectedReward = reward
          break
        }
      }
      
      setTimeout(() => {
        isDrawing.value = false
        currentBox.value = -1
        alert(`Congratulations Player ${playerId.value}! You got: ${selectedReward.name}`)
      }, 500)
      
      return
    }
    
    setTimeout(animate, speed)
  }
  
  animate()
}

useHead({
  title: 'MidasBuy'
})
</script>

<style scoped>
/* Base styles (Mobile First - 320px and up) */
.background-container {
  position: relative;
  width: 100vw;
  min-height: 150vh;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

.header-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 40px;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.85);
}

.header {
  padding: 0 15px;
  height: 100%;
}

.header__logoWrap {
  gap: 10px;
}

.header__widthAuto img {
  max-height: 24px;
}

.lottery-container {
  position: relative;
  width: 250px;
  margin: 60px auto 0;
  padding: 10px;
}

.lottery-wrapper {
  padding: 8px;
}

.prize-grid {
  gap: 6px;
}

.prize-row,
.prize-middle {
  gap: 6px;
}

.wrapper_3Yyoa {
  width: 60px;
  height: 60px;
}

.box-prize_Ece33 {
  border-width: 1px;
}

.center-display {
  padding: 8px;
  min-height: 160px;
}

.featured-item {
  padding: 10px;
}

.draw-btn {
  padding: 10px 20px;
  font-size: 14px;
  margin-top: 15px;
}

/* Small Mobile (375px) */
@media screen and (min-width: 375px) {
  .lottery-container {
    width: 320px;
  }

  .wrapper_3Yyoa {
    width: 65px;
    height: 65px;
  }
}

/* Medium Mobile (425px) */
@media screen and (min-width: 425px) {
  .lottery-container {
    width: 350px;
    margin-top: 70px;
  }

  .wrapper_3Yyoa {
    width: 70px;
    height: 70px;
  }

  .header {
    padding: 0 20px;
  }
}

/* Tablet (768px) */
@media screen and (min-width: 768px) {
  .header-wrap {
    height: 45px;
  }

  .header {
    max-width: 720px;
    margin: 0 auto;
  }

  .header__widthAuto img {
    max-height: 28px;
  }

  .lottery-container {
    width: 400px;
    margin-top: 80px;
  }

  .lottery-wrapper {
    padding: 12px;
  }

  .prize-grid {
    gap: 8px;
  }

  .prize-row,
  .prize-middle {
    gap: 8px;
  }

  .wrapper_3Yyoa {
    width: 80px;
    height: 80px;
  }

  .center-display {
    padding: 12px;
    min-height: 200px;
  }

  .draw-btn {
    padding: 12px 30px;
    font-size: 16px;
  }
}

/* Small Desktop (1024px) */
@media screen and (min-width: 1024px) {
  .header {
    max-width: 960px;
  }

  .lottery-container {
    width: 450px;
    margin-top: 90px;
  }

  .lottery-wrapper {
    padding: 15px;
  }

  .prize-grid {
    gap: 10px;
  }

  .prize-row,
  .prize-middle {
    gap: 10px;
  }

  .wrapper_3Yyoa {
    width: 90px;
    height: 90px;
  }

  .center-display {
    padding: 15px;
    min-height: 220px;
  }

  .draw-btn {
    padding: 15px 35px;
    font-size: 18px;
  }
}

/* Large Desktop (1440px) */
@media screen and (min-width: 1440px) {
  .header {
    max-width: 1200px;
  }

  .lottery-container {
    width: 500px;
    margin-top: 100px;
  }

  .wrapper_3Yyoa {
    width: 100px;
    height: 100px;
  }

  .center-display {
    min-height: 250px;
  }

  .draw-btn {
    padding: 15px 40px;
  }
}

/* 4K and Ultra-wide (1920px+) */
@media screen and (min-width: 1920px) {
  .header {
    max-width: 1400px;
  }

  .lottery-container {
    width: 550px;
  }

  .wrapper_3Yyoa {
    width: 110px;
    height: 110px;
  }

  .center-display {
    min-height: 280px;
  }
}

/* Handle Landscape Mode */
@media screen and (max-height: 600px) and (orientation: landscape) {
  .lottery-container {
    margin-top: 50px;
    transform: scale(0.8);
    transform-origin: top center;
  }
}

/* Handle Very Small Screens */
@media screen and (max-width: 320px) {
  .lottery-container {
    width: 280px;
    margin-top: 50px;
  }

  .wrapper_3Yyoa {
    width: 55px;
    height: 55px;
  }

  .center-display {
    min-height: 140px;
  }

  .draw-btn {
    padding: 8px 16px;
    font-size: 12px;
  }
}

/* Handle Tall Screens */
@media screen and (min-height: 1000px) {
  .lottery-container {
    margin-top: 120px;
  }
}

/* High DPI Screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .box-prize_Ece33 {
    border-width: 0.5px;
  }
}

/* Ensure proper touch targets on touch devices */
@media (hover: none) and (pointer: coarse) {
  .draw-btn {
    min-height: 44px;
  }

  .wrapper_3Yyoa {
    min-width: 44px;
    min-height: 44px;
  }
}

/* Reduce Motion */
@media (prefers-reduced-motion: reduce) {
  .wrapper_3Yyoa,
  .box-prize_Ece33,
  .draw-btn {
    transition: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .header-wrap {
    background-color: rgba(0, 0, 0, 0.95);
  }

  .box-prize_Ece33 {
    border-color: rgba(255, 255, 255, 0.2);
  }
}

/* Print Styles */
@media print {
  .lottery-container {
    break-inside: avoid;
    margin: 20px auto;
  }

  .draw-btn {
    display: none;
  }
}

/* Snow Background */
.snow-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 150vh;
  object-fit: cover;
  object-position: top center;
  z-index: -1;
  margin: 0;
  padding: 0;
}

/* Ensure container is above background */
.lottery-container {
  position: relative;
  z-index: 1;
}

/* Add smooth transitions */
.lottery-wrapper,
.wrapper_3Yyoa,
.box-prize_Ece33,
.draw-btn {
  transition: all 0.3s ease;
}

/* Improve accessibility */
@media (prefers-contrast: more) {
  .box-prize_Ece33 {
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.8);
  }

  .draw-btn {
    border: 2px solid #fff;
  }
}

/* Ensure proper background on mobile */
@media screen and (max-width: 768px) {
  .background-container {
    min-height: 150vh;
  }
  
  .snow-background {
    height: 150vh;
  }
}

/* Handle orientation changes */
@media screen and (orientation: landscape) {
  .snow-background {
    height: 150vh;
    width: 100%;
  }
}

/* Handle ultra-wide screens */
@media screen and (min-width: 1920px) {
  .snow-background {
    width: 100%;
  }
}

/* Handle iOS Safari */
@supports (-webkit-touch-callout: none) {
  .background-container,
  .snow-background {
    min-height: calc(-webkit-fill-available + 50vh);
  }
}

/* Add highlight effect for reward boxes */
.reward-box {
  position: relative;
  transition: transform 0.2s;
}

.reward-box::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 3px solid transparent;
  border-radius: 8px;
  transition: border-color 0.2s;
}

.reward-box.active::after {
  border-color: #FFD700;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

/* Add hover effect */
.reward-box:hover {
  transform: scale(1.05);
}

/* Main items styling */
.main-item {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.main-item::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 4px solid transparent;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.main-item.active::after {
  border-color: #FFD700;
  box-shadow: 0 0 25px rgba(255, 215, 0, 0.7);
  animation: pulse 1s infinite;
}

.main-item:hover {
  transform: scale(1.05);
}

.item-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px;
  text-align: center;
  font-size: 12px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.main-item:hover .item-name {
  transform: translateY(0);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.7);
  }
  50% {
    box-shadow: 0 0 35px rgba(255, 215, 0, 0.9);
  }
  100% {
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.7);
  }
}
</style> 