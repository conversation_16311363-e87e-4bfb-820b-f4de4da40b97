<template>
  <div class="bg-white">
    <div class="max-w-2xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:max-w-7xl lg:px-8">
      <h1 class="text-3xl font-extrabold tracking-tight text-gray-900">Our Products</h1>
      
      <!-- Filters -->
      <div class="mt-6 flex items-center justify-between">
        <div class="flex items-center">
          <select v-model="selectedCategory" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
            <option value="">All Categories</option>
            <option value="credits">Game Credits</option>
            <option value="passes">Battle Passes</option>
            <option value="items">Special Items</option>
          </select>
        </div>
        <div class="flex items-center">
          <select v-model="sortBy" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
            <option value="price-asc">Price: Low to High</option>
            <option value="price-desc">Price: High to Low</option>
            <option value="name">Name</option>
          </select>
        </div>
      </div>

      <!-- Product Grid -->
      <div class="mt-8 grid grid-cols-1 gap-y-12 sm:grid-cols-2 sm:gap-x-6 lg:grid-cols-4 xl:gap-x-8">
        <div v-for="product in filteredProducts" :key="product.id" class="relative">
          <div class="relative w-full h-72 rounded-lg overflow-hidden">
            <div class="w-full h-full bg-indigo-100 flex items-center justify-center">
              <span class="text-indigo-600 text-6xl">{{ product.icon }}</span>
            </div>
          </div>
          <div class="relative mt-4">
            <h3 class="text-sm font-medium text-gray-900">{{ product.name }}</h3>
            <p class="mt-1 text-sm text-gray-500">{{ product.description }}</p>
          </div>
          <div class="absolute top-0 inset-x-0 h-72 rounded-lg p-4 flex items-end justify-end overflow-hidden">
            <div aria-hidden="true" class="absolute inset-x-0 bottom-0 h-36 bg-gradient-to-t from-black opacity-50"></div>
            <p class="relative text-lg font-semibold text-white">{{ product.price }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const selectedCategory = ref('')
const sortBy = ref('price-asc')

const products = [
  {
    id: 1,
    name: '1000 Game Credits',
    description: 'Basic credit pack for in-game purchases',
    price: '$9.99',
    category: 'credits',
    icon: '💎'
  },
  {
    id: 2,
    name: '2500 Game Credits',
    description: 'Medium credit pack with bonus',
    price: '$19.99',
    category: 'credits',
    icon: '💎'
  },
  {
    id: 3,
    name: 'Season Battle Pass',
    description: 'Access to premium seasonal content',
    price: '$24.99',
    category: 'passes',
    icon: '🎮'
  },
  {
    id: 4,
    name: 'Premium Bundle',
    description: 'Special items collection',
    price: '$29.99',
    category: 'items',
    icon: '🎁'
  },
  {
    id: 5,
    name: '5000 Game Credits',
    description: 'Large credit pack with extra bonus',
    price: '$39.99',
    category: 'credits',
    icon: '💎'
  },
  {
    id: 6,
    name: 'Elite Pass',
    description: 'Premium pass with exclusive rewards',
    price: '$49.99',
    category: 'passes',
    icon: '🎮'
  }
]

const filteredProducts = computed(() => {
  let filtered = [...products]
  
  if (selectedCategory.value) {
    filtered = filtered.filter(p => p.category === selectedCategory.value)
  }
  
  switch (sortBy.value) {
    case 'price-asc':
      filtered.sort((a, b) => parseFloat(a.price.slice(1)) - parseFloat(b.price.slice(1)))
      break
    case 'price-desc':
      filtered.sort((a, b) => parseFloat(b.price.slice(1)) - parseFloat(a.price.slice(1)))
      break
    case 'name':
      filtered.sort((a, b) => a.name.localeCompare(b.name))
      break
  }
  
  return filtered
})
</script>

<script>
export default {
  name: 'ProductsPage',
  head() {
    return {
      title: 'MidasBuy - Products'
    }
  },
  data() {
    return {
      products: [
        {
          id: 1,
          name: 'Game Credits',
          href: '#',
          imageSrc: '/images/products/credits.jpg',
          description: '1000 Game Credits',
          price: '$9.99'
        },
        {
          id: 2,
          name: 'Premium Pass',
          href: '#',
          imageSrc: '/images/products/pass.jpg',
          description: 'Season Pass',
          price: '$19.99'
        },
        // Add more products as needed
      ]
    }
  }
}
</script> 