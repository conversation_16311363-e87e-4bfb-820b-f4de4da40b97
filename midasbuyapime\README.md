# MidasBuy

A modern e-commerce platform for gaming credits and items built with Nuxt.js.

## Features

- Modern and responsive design using Tailwind CSS
- Server-side rendering with Nuxt.js
- Dynamic product catalog
- Secure payment integration (coming soon)
- User authentication (coming soon)

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn

## Setup

1. Clone the repository:
```bash
git clone https://github.com/yourusername/midasbuyapime.git
cd midasbuyapime
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
```

4. Build for production:
```bash
npm run build
# or
yarn build
```

5. Start production server:
```bash
npm run start
# or
yarn start
```

## Environment Variables

Create a `.env` file in the root directory and add the following variables:
```
BASE_URL=http://localhost:3000
API_URL=your_api_url_here
```

## Project Structure

- `assets/` - Static assets like images and global CSS
- `components/` - Vue components
- `layouts/` - Page layouts
- `pages/` - Application pages and routes
- `static/` - Static files served directly
- `store/` - Vuex store files (coming soon)

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details. 