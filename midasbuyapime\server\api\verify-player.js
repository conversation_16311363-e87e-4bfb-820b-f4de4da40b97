export default defineEventHandler(async (event) => {
  // Only allow GET requests
  if (getMethod(event) !== 'GET') {
    throw createError({
      statusCode: 405,
      statusMessage: 'Method Not Allowed'
    })
  }

  const query = getQuery(event)
  const { playerId } = query

  if (!playerId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Player ID is required'
    })
  }

  try {
    const response = await fetch(`https://api.neferbyte.com/game-id-checker/pubgm-global/${playerId}`, {
      headers: {
        "x-Api-key": "********************************"
      }
    })

    if (!response.ok) {
      throw createError({
        statusCode: response.status,
        statusMessage: `API request failed: ${response.statusText}`
      })
    }

    const data = await response.json()
    return data
  } catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to verify player ID'
    })
  }
}) 